{% extends "admin/base.html" %}

{% block title %}属性管理 - 管理后台{% endblock %}

{% block extra_css %}
<style>
/* 现代化页面样式 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin: -1.5rem -1.5rem 2rem -1.5rem;
    border-radius: 0 0 1rem 1rem;
}

.page-title h1 {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.page-title p {
    opacity: 0.9;
    font-size: 0.95rem;
}

.btn-modern {
    border-radius: 0.5rem;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-modern:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 筛选区域样式 */
.filter-section {
    margin-bottom: 2rem;
}

.filter-card {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.filter-header {
    background: #f8f9fc;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e3e6f0;
}

.filter-title {
    margin: 0;
    color: #5a5c69;
    font-weight: 600;
}

.filter-body {
    padding: 1.5rem;
}

.form-select-modern {
    border-radius: 0.5rem;
    border: 1px solid #d1d3e2;
    padding: 0.6rem 1rem;
    transition: all 0.3s ease;
}

.form-select-modern:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stats-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.stats-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stats-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* 内容区域样式 */
.content-section {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #e3e6f0;
    overflow: hidden;
}

.section-header {
    background: #f8f9fc;
    padding: 1.5rem;
    border-bottom: 1px solid #e3e6f0;
}

.section-title {
    margin: 0;
    color: #5a5c69;
    font-weight: 600;
}

.badge-modern {
    background: #667eea;
    color: white;
    border-radius: 1rem;
    padding: 0.4rem 0.8rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.section-body {
    padding: 0;
}

/* 表格样式 */
.table-container {
    overflow-x: auto;
}

.table-modern {
    margin: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table-modern thead th {
    background: #f8f9fc;
    border: none;
    padding: 1rem;
    font-weight: 600;
    color: #5a5c69;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-row-modern {
    transition: all 0.3s ease;
    border-bottom: 1px solid #e3e6f0;
}

.table-row-modern:hover {
    background-color: #f8f9fc;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table-row-modern td {
    padding: 1rem;
    vertical-align: middle;
    border: none;
}

/* 表格内容样式 */
.id-badge {
    background: #e3e6f0;
    color: #5a5c69;
    padding: 0.3rem 0.6rem;
    border-radius: 0.5rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.attribute-info {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.attribute-name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.95rem;
}

.attribute-meta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.group-badge {
    background: #ffeaa7;
    color: #2d3436;
    padding: 0.2rem 0.6rem;
    border-radius: 0.4rem;
    font-size: 0.75rem;
    font-weight: 500;
}

.category-text {
    color: #74b9ff;
    font-size: 0.8rem;
}

.value-badge {
    background: #74b9ff;
    color: white;
    padding: 0.3rem 0.6rem;
    border-radius: 0.4rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.price-badge {
    padding: 0.3rem 0.6rem;
    border-radius: 0.4rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.price-percentage {
    background: #74b9ff;
    color: white;
}

.price-fixed {
    background: #00b894;
    color: white;
}

.sort-badge {
    background: #e3e6f0;
    color: #5a5c69;
    padding: 0.3rem 0.6rem;
    border-radius: 0.4rem;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-badge {
    padding: 0.3rem 0.6rem;
    border-radius: 0.4rem;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-active {
    background: #00b894;
    color: white;
}

.status-inactive {
    background: #636e72;
    color: white;
}

.date-text {
    color: #74b9ff;
    font-size: 0.85rem;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.btn-action {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.5rem;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.btn-edit {
    background: #74b9ff;
    color: white;
}

.btn-edit:hover {
    background: #0984e3;
    color: white;
    transform: translateY(-1px);
}

.btn-delete {
    background: #fd79a8;
    color: white;
}

.btn-delete:hover {
    background: #e84393;
    color: white;
    transform: translateY(-1px);
}

/* 分页样式 */
.pagination-section {
    padding: 1.5rem;
    background: #f8f9fc;
    border-top: 1px solid #e3e6f0;
}

.pagination-modern {
    margin: 0;
    justify-content: center;
}

.pagination-modern .page-link {
    border: none;
    border-radius: 0.5rem;
    margin: 0 0.2rem;
    padding: 0.6rem 1rem;
    color: #5a5c69;
    background: white;
    transition: all 0.3s ease;
}

.pagination-modern .page-link:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

.pagination-modern .page-item.active .page-link {
    background: #667eea;
    color: white;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: #74b9ff;
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    opacity: 0.6;
}

.empty-state-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #2c3e50;
}

.empty-state-description {
    font-size: 1rem;
    margin-bottom: 2rem;
    color: #74b9ff;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-state-action {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* 模态框样式 */
.modal-modern .modal-content {
    border: none;
    border-radius: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header-modern {
    background: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-radius: 1rem 1rem 0 0;
    padding: 1.5rem;
}

.modal-title-wrapper {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.modal-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    background: rgba(255, 193, 7, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.modal-body-modern {
    padding: 2rem 1.5rem;
}

.delete-confirmation {
    text-align: center;
}

.confirmation-text {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0.5rem;
    padding: 1rem;
    color: #856404;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-footer-modern {
    background: #f8f9fc;
    border-top: 1px solid #e3e6f0;
    border-radius: 0 0 1rem 1rem;
    padding: 1.5rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.btn-close-modern {
    background: none;
    border: none;
    font-size: 1.2rem;
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.btn-close-modern:hover {
    opacity: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        margin: -1rem -1rem 1.5rem -1rem;
        padding: 1.5rem 0;
    }

    .filter-section .row {
        flex-direction: column;
    }

    .stats-card {
        margin-top: 1rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .empty-state-action {
        flex-direction: column;
        align-items: center;
    }

    .modal-header-modern,
    .modal-body-modern,
    .modal-footer-modern {
        padding: 1rem;
    }

    .modal-title-wrapper {
        gap: 0.5rem;
    }

    .modal-icon {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- 现代化页面头部 -->
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div class="page-title">
            <h1 class="h2 mb-1">
                <i class="fas fa-tags me-3 text-primary"></i>属性管理
            </h1>
            {% if current_group %}
            <p class="text-muted mb-0">
                <i class="fas fa-tag me-1"></i>{{ current_group.category.name if current_group.category else '未分类' }} / {{ current_group.name }}
            </p>
            {% else %}
            <p class="text-muted mb-0">管理商品的详细属性信息</p>
            {% endif %}
        </div>
        <div class="page-actions">
            {% if current_group %}
            <a href="{{ url_for('admin.add_attribute', group_id=current_group.id) }}"
               class="btn btn-primary btn-modern">
                <i class="fas fa-plus me-2"></i>添加属性
            </a>
            {% else %}
            <a href="{{ url_for('admin.add_attribute') }}" class="btn btn-primary btn-modern">
                <i class="fas fa-plus me-2"></i>添加属性
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- 现代化筛选区域 -->
{% if not current_group %}
<div class="filter-section">
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="filter-card">
                <div class="filter-header">
                    <h6 class="filter-title">
                        <i class="fas fa-filter me-2"></i>筛选条件
                    </h6>
                </div>
                <div class="filter-body">
                    <form method="GET" class="filter-form">
                        <div class="row g-3">
                            <div class="col-md-8">
                                <label class="form-label">按属性组筛选</label>
                                <select name="group_id" class="form-select form-select-modern" onchange="this.form.submit()">
                                    <option value="">-- 所有属性组 --</option>
                                    {% for group in groups %}
                                    <option value="{{ group.id }}" {{ 'selected' if group.id == current_group_id }}>
                                        {{ group.category.name if group.category else '未分类' }} - {{ group.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary btn-modern flex-fill">
                                        <i class="fas fa-search me-1"></i>筛选
                                    </button>
                                    <a href="{{ url_for('admin.attributes') }}" class="btn btn-outline-secondary btn-modern">
                                        <i class="fas fa-times"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="stats-card">
                <div class="stats-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <div class="stats-content">
                    <div class="stats-number">{{ attributes.total if attributes else 0 }}</div>
                    <div class="stats-label">个属性</div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 现代化属性列表 -->
<div class="content-section">
    <div class="section-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="section-title">
                <i class="fas fa-list me-2"></i>属性列表
                {% if current_group %}
                <span class="text-muted">- {{ current_group.name }}</span>
                {% endif %}
            </h5>
            {% if attributes and attributes.items %}
            <div class="section-badge">
                <span class="badge badge-modern">{{ attributes.total }} 个属性</span>
            </div>
            {% endif %}
        </div>
    </div>

    <div class="section-body">
        {% if attributes and attributes.items %}
        <div class="table-container">
            <table class="table table-modern">
                <thead>
                    <tr>
                        <th class="text-center" style="width: 60px;">ID</th>
                        <th>属性信息</th>
                        <th class="text-center" style="width: 120px;">属性值</th>
                        <th class="text-center" style="width: 120px;">价格调整</th>
                        <th class="text-center" style="width: 80px;">排序</th>
                        <th class="text-center" style="width: 100px;">状态</th>
                        <th class="text-center" style="width: 120px;">创建时间</th>
                        <th class="text-center" style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attribute in attributes.items %}
                    <tr class="table-row-modern">
                        <td class="text-center">
                            <span class="id-badge">#{{ attribute.id }}</span>
                        </td>
                        <td>
                            <div class="attribute-info">
                                <div class="attribute-name">{{ attribute.name }}</div>
                                <div class="attribute-meta">
                                    {% if current_group %}
                                    <span class="group-badge">{{ current_group.name }}</span>
                                    <span class="category-text">
                                        <i class="fas fa-folder me-1"></i>{{ current_group.category.name if current_group.category else '未分类' }}
                                    </span>
                                    {% else %}
                                    <span class="group-badge">{{ attribute.group.name if attribute.group else '未分组' }}</span>
                                    <span class="category-text">
                                        <i class="fas fa-folder me-1"></i>{{ attribute.group.category.name if attribute.group and attribute.group.category else '未分类' }}
                                    </span>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="text-center">
                            {% if attribute.value %}
                            <span class="value-badge">{{ attribute.value }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if attribute.price_modifier and attribute.price_modifier != 0 %}
                                {% if attribute.price_modifier_type == 'percentage' %}
                                    <span class="price-badge price-percentage">
                                        <i class="fas fa-percentage me-1"></i>+{{ attribute.price_modifier }}%
                                    </span>
                                {% else %}
                                    <span class="price-badge price-fixed">
                                        <i class="fas fa-yen-sign me-1"></i>+{{ attribute.price_modifier }}
                                    </span>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">
                                    <i class="fas fa-minus"></i>
                                </span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <span class="sort-badge">{{ attribute.sort_order or 0 }}</span>
                        </td>
                        <td class="text-center">
                            {% if attribute.is_active %}
                                <span class="status-badge status-active">
                                    <i class="fas fa-check me-1"></i>启用
                                </span>
                            {% else %}
                                <span class="status-badge status-inactive">
                                    <i class="fas fa-times me-1"></i>禁用
                                </span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <span class="date-text">{{ attribute.created_at.strftime('%Y-%m-%d') if attribute.created_at else '-' }}</span>
                        </td>
                        <td class="text-center">
                            <div class="action-buttons">
                                <a href="{{ url_for('admin.edit_attribute', id=attribute.id) }}"
                                   class="btn btn-action btn-edit" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-action btn-delete delete-btn"
                                        data-url="{{ url_for('admin.delete_attribute', id=attribute.id) }}"
                                        data-name="{{ attribute.name }}" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 现代化分页 -->
        {% if attributes.pages > 1 %}
        <div class="pagination-section">
            <nav aria-label="属性分页">
                <ul class="pagination pagination-modern">
                    {% if attributes.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.attributes', page=attributes.prev_num, group_id=current_group_id if current_group_id else '') }}">
                            <i class="fas fa-chevron-left me-1"></i>上一页
                        </a>
                    </li>
                    {% endif %}

                    {% for page_num in attributes.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != attributes.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.attributes', page=page_num, group_id=current_group_id if current_group_id else '') }}">{{ page_num }}</a>
                            </li>
                            {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                            {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">…</span>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if attributes.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.attributes', page=attributes.next_num, group_id=current_group_id if current_group_id else '') }}">
                            下一页<i class="fas fa-chevron-right ms-1"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}

        {% else %}
        <!-- 现代化无数据状态 -->
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-tags"></i>
            </div>
            <h5 class="empty-state-title">
                {% if current_group %}
                {{ current_group.name }} 暂无属性
                {% else %}
                暂无属性数据
                {% endif %}
            </h5>
            <p class="empty-state-description">
                {% if current_group %}
                该属性组下还没有任何属性，点击下方按钮添加第一个属性
                {% else %}
                系统中还没有任何属性，请先创建属性组，然后添加相应的属性
                {% endif %}
            </p>
            <div class="empty-state-action">
                {% if current_group %}
                <a href="{{ url_for('admin.add_attribute', group_id=current_group.id) }}" class="btn btn-primary btn-modern">
                    <i class="fas fa-plus me-2"></i>添加属性
                </a>
                {% else %}
                <a href="{{ url_for('admin.attribute_groups') }}" class="btn btn-primary btn-modern me-2">
                    <i class="fas fa-layer-group me-2"></i>管理属性组
                </a>
                <a href="{{ url_for('admin.add_attribute') }}" class="btn btn-outline-primary btn-modern">
                    <i class="fas fa-plus me-2"></i>添加属性
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 现代化删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modal-modern">
            <div class="modal-header modal-header-modern">
                <div class="modal-title-wrapper">
                    <div class="modal-icon">
                        <i class="fas fa-exclamation-triangle text-warning"></i>
                    </div>
                    <h5 class="modal-title">确认删除</h5>
                </div>
                <button type="button" class="btn-close btn-close-modern" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body modal-body-modern">
                <div class="delete-confirmation">
                    <p class="confirmation-text">确定要删除属性 "<span id="deleteAttributeName" class="text-primary fw-bold"></span>" 吗？</p>
                    <div class="warning-box">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>删除后无法恢复，请谨慎操作。</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer modal-footer-modern">
                <button type="button" class="btn btn-secondary btn-modern" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>取消
                </button>
                <button type="button" class="btn btn-danger btn-modern" id="confirmDelete">
                    <i class="fas fa-trash me-2"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 删除功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 初始化属性页面删除功能');
    
    // 初始化删除功能
    initDeleteFunction();
});

function initDeleteFunction() {
    const deleteModal = document.getElementById('deleteModal');
    const deleteNameSpan = document.getElementById('deleteAttributeName');
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    let currentDeleteUrl = '';
    let currentDeleteName = '';
    
    if (!deleteModal) {
        console.error('删除模态框未找到');
        return;
    }
    
    const modal = new bootstrap.Modal(deleteModal);
    
    // 移除所有现有的事件监听器
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(btn => {
        // 克隆节点以移除所有事件监听器
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
    });
    
    // 重新绑定删除按钮事件
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            currentDeleteUrl = this.dataset.url;
            currentDeleteName = this.dataset.name;
            
            console.log('点击删除按钮:', currentDeleteName, currentDeleteUrl);
            
            if (deleteNameSpan) {
                deleteNameSpan.textContent = currentDeleteName;
            }
            
            modal.show();
        });
    });
    
    // 确认删除事件
    if (confirmDeleteBtn) {
        // 移除旧的事件监听器
        const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
        confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
        
        // 绑定新的事件监听器
        document.getElementById('confirmDelete').addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!currentDeleteUrl) {
                showAlert('danger', '删除URL无效');
                return;
            }
            
            // 禁用按钮，显示加载状态
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
            
            // 获取CSRF token
            const csrfToken = getCSRFToken();
            
            console.log('发送删除请求:', currentDeleteUrl);
            console.log('CSRF Token:', csrfToken);
            
            // 发送删除请求
            fetch(currentDeleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return response.json();
            })
            .then(data => {
                console.log('删除响应:', data);
                
                if (data.success) {
                    showAlert('success', data.message || '删除成功');
                    // 延迟刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', data.message || '删除失败');
                    this.disabled = false;
                    this.innerHTML = '确认删除';
                }
            })
            .catch(error => {
                console.error('删除请求错误:', error);
                showAlert('danger', `删除失败: ${error.message}`);
                this.disabled = false;
                this.innerHTML = '确认删除';
            });
            
            // 隐藏模态框
            modal.hide();
        });
    }
}

function getCSRFToken() {
    // 尝试多种方式获取CSRF token
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    
    const inputToken = document.querySelector('input[name="csrf_token"]');
    if (inputToken) {
        return inputToken.value;
    }
    
    // 最后尝试模板变量
    return '{{ csrf_token() }}';
}

function showAlert(type, message) {
    // 移除现有的alert
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="margin-bottom: 1rem;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // 滚动到顶部显示消息
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        // 自动关闭
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>

{% endblock %}
