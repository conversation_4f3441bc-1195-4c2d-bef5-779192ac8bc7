{% extends "admin/base.html" %}

{% block title %}属性管理 - 管理后台{% endblock %}

{% block content %}

<!-- 页面标题和操作按钮 -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h4 class="mb-1">
            <i class="fas fa-tags text-primary me-2"></i>属性管理
        </h4>
        {% if current_group %}
        <p class="text-muted mb-0">
            <i class="fas fa-folder me-1"></i>{{ current_group.category.name if current_group.category else '未分类' }}
            <i class="fas fa-chevron-right mx-2"></i>{{ current_group.name }}
        </p>
        {% else %}
        <p class="text-muted mb-0">管理所有属性</p>
        {% endif %}
    </div>
    <div>
        {% if current_group %}
        <a href="{{ url_for('admin.add_attribute', group_id=current_group.id) }}"
           class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>添加属性
        </a>
        {% else %}
        <a href="{{ url_for('admin.add_attribute') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>添加属性
        </a>
        {% endif %}
    </div>
</div>

<!-- 导航面包屑 -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="{{ url_for('admin.attribute_groups') }}" class="text-decoration-none">
                <i class="fas fa-layer-group me-1"></i>属性组管理
            </a>
        </li>
        {% if current_group %}
        <li class="breadcrumb-item active" aria-current="page">{{ current_group.name }}</li>
        {% else %}
        <li class="breadcrumb-item active" aria-current="page">所有属性</li>
        {% endif %}
    </ol>
</nav>

<!-- 筛选和统计 -->
{% if not current_group %}
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <form method="GET" class="row g-3 align-items-end">
                    <div class="col-md-8">
                        <label class="form-label fw-bold">
                            <i class="fas fa-filter me-1"></i>按属性组筛选
                        </label>
                        <select name="group_id" class="form-select" onchange="this.form.submit()">
                            <option value="">-- 所有属性组 --</option>
                            {% for group in groups %}
                            <option value="{{ group.id }}" {{ 'selected' if group.id == current_group_id }}>
                                {{ group.category.name if group.category else '未分类' }} - {{ group.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-search me-1"></i>筛选
                            </button>
                            <a href="{{ url_for('admin.attributes') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i>清除
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 shadow-sm bg-primary text-white">
            <div class="card-body text-center">
                <h3 class="mb-1">{{ attributes.total }}</h3>
                <p class="mb-0">
                    <i class="fas fa-tags me-1"></i>个属性
                </p>
            </div>
        </div>
    </div>
</div>
{% endif %}

<div class="card border-0 shadow-sm">
    <div class="card-header bg-white border-bottom">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>属性列表
                {% if current_group %}
                <small class="text-muted">- {{ current_group.name }}</small>
                {% endif %}
            </h5>
            {% if attributes.items %}
            <span class="badge bg-primary">{{ attributes.total }} 个属性</span>
            {% endif %}
        </div>
    </div>

    <div class="card-body p-0">
        {% if attributes.items %}
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        <th class="text-center" style="width: 60px;">ID</th>
                        <th>属性信息</th>
                        <th class="text-center" style="width: 120px;">属性值</th>
                        <th class="text-center" style="width: 120px;">价格调整</th>
                        <th class="text-center" style="width: 80px;">排序</th>
                        <th class="text-center" style="width: 100px;">状态</th>
                        <th class="text-center" style="width: 120px;">创建时间</th>
                        <th class="text-center" style="width: 120px;">操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for attribute in attributes.items %}
                    <tr>
                        <td class="text-center">
                            <span class="badge bg-light text-dark">#{{ attribute.id }}</span>
                        </td>
                        <td>
                            <div>
                                <strong class="text-dark">{{ attribute.name }}</strong>
                                {% if current_group %}
                                <span class="badge bg-warning text-dark ms-2">{{ current_group.name }}</span>
                                {% else %}
                                <span class="badge bg-secondary ms-2">{{ attribute.group.name if attribute.group else '未分组' }}</span>
                                {% endif %}
                            </div>
                            {% if current_group %}
                            <small class="text-muted d-block mt-1">
                                <i class="fas fa-folder me-1"></i>{{ current_group.category.name if current_group.category else '未分类' }}
                            </small>
                            {% else %}
                            <small class="text-muted d-block mt-1">
                                <i class="fas fa-folder me-1"></i>{{ attribute.group.category.name if attribute.group and attribute.group.category else '未分类' }}
                            </small>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if attribute.value %}
                            <span class="badge bg-info">{{ attribute.value }}</span>
                            {% else %}
                            <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            {% if attribute.price_modifier and attribute.price_modifier != 0 %}
                                {% if attribute.price_modifier_type == 'percentage' %}
                                    <span class="badge bg-info">
                                        <i class="fas fa-percentage me-1"></i>+{{ attribute.price_modifier }}%
                                    </span>
                                {% else %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-yen-sign me-1"></i>+{{ attribute.price_modifier }}
                                    </span>
                                {% endif %}
                            {% else %}
                                <span class="text-muted">
                                    <i class="fas fa-minus"></i>
                                </span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <span class="badge bg-light text-dark">{{ attribute.sort_order or 0 }}</span>
                        </td>
                        <td class="text-center">
                            {% if attribute.is_active %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>启用
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-times me-1"></i>禁用
                                </span>
                            {% endif %}
                        </td>
                        <td class="text-center">
                            <small class="text-muted">{{ attribute.created_at.strftime('%Y-%m-%d') if attribute.created_at else '-' }}</small>
                        </td>
                        <td class="text-center">
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('admin.edit_attribute', id=attribute.id) }}"
                                   class="btn btn-outline-primary" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button type="button" class="btn btn-outline-danger delete-btn"
                                        data-url="{{ url_for('admin.delete_attribute', id=attribute.id) }}"
                                        data-name="{{ attribute.name }}" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        <!-- 分页 -->
        {% if attributes.pages > 1 %}
        <nav aria-label="属性分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if attributes.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.attributes', page=attributes.prev_num, group_id=current_group_id if current_group_id else '') }}">上一页</a>
                </li>
                {% endif %}
                
                {% for page_num in attributes.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != attributes.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('admin.attributes', page=page_num, group_id=current_group_id if current_group_id else '') }}">{{ page_num }}</a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if attributes.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('admin.attributes', page=attributes.next_num, group_id=current_group_id if current_group_id else '') }}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        
        {% else %}
        <div class="text-center py-5">
            <div class="mb-4">
                <i class="fas fa-tags fa-4x text-muted"></i>
            </div>
            <h5 class="text-muted mb-3">暂无属性数据</h5>
            <p class="text-muted mb-4">
                {% if current_group %}
                "{{ current_group.name }}" 属性组下还没有任何属性，点击下方按钮添加第一个属性
                {% else %}
                系统中还没有任何属性，请先创建属性组，然后添加相应的属性
                {% endif %}
            </p>
            {% if current_group %}
            <a href="{{ url_for('admin.add_attribute', group_id=current_group.id) }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>添加属性
            </a>
            {% else %}
            <a href="{{ url_for('admin.attribute_groups') }}" class="btn btn-primary me-2">
                <i class="fas fa-layer-group me-2"></i>管理属性组
            </a>
            <a href="{{ url_for('admin.add_attribute') }}" class="btn btn-outline-primary">
                <i class="fas fa-plus me-2"></i>添加属性
            </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>确定要删除属性 "<span id="deleteAttributeName"></span>" 吗？</p>
                <p class="text-muted small">此操作不可恢复，请谨慎操作。</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<script>
// 删除功能
document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 初始化属性页面删除功能');
    
    // 初始化删除功能
    initDeleteFunction();
});

function initDeleteFunction() {
    const deleteModal = document.getElementById('deleteModal');
    const deleteNameSpan = document.getElementById('deleteAttributeName');
    const confirmDeleteBtn = document.getElementById('confirmDelete');
    let currentDeleteUrl = '';
    let currentDeleteName = '';
    
    if (!deleteModal) {
        console.error('删除模态框未找到');
        return;
    }
    
    const modal = new bootstrap.Modal(deleteModal);
    
    // 移除所有现有的事件监听器
    const deleteButtons = document.querySelectorAll('.delete-btn');
    deleteButtons.forEach(btn => {
        // 克隆节点以移除所有事件监听器
        const newBtn = btn.cloneNode(true);
        btn.parentNode.replaceChild(newBtn, btn);
    });
    
    // 重新绑定删除按钮事件
    document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            currentDeleteUrl = this.dataset.url;
            currentDeleteName = this.dataset.name;
            
            console.log('点击删除按钮:', currentDeleteName, currentDeleteUrl);
            
            if (deleteNameSpan) {
                deleteNameSpan.textContent = currentDeleteName;
            }
            
            modal.show();
        });
    });
    
    // 确认删除事件
    if (confirmDeleteBtn) {
        // 移除旧的事件监听器
        const newConfirmBtn = confirmDeleteBtn.cloneNode(true);
        confirmDeleteBtn.parentNode.replaceChild(newConfirmBtn, confirmDeleteBtn);
        
        // 绑定新的事件监听器
        document.getElementById('confirmDelete').addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            if (!currentDeleteUrl) {
                showAlert('danger', '删除URL无效');
                return;
            }
            
            // 禁用按钮，显示加载状态
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>删除中...';
            
            // 获取CSRF token
            const csrfToken = getCSRFToken();
            
            console.log('发送删除请求:', currentDeleteUrl);
            console.log('CSRF Token:', csrfToken);
            
            // 发送删除请求
            fetch(currentDeleteUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                console.log('响应状态:', response.status);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return response.json();
            })
            .then(data => {
                console.log('删除响应:', data);
                
                if (data.success) {
                    showAlert('success', data.message || '删除成功');
                    // 延迟刷新页面
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('danger', data.message || '删除失败');
                    this.disabled = false;
                    this.innerHTML = '确认删除';
                }
            })
            .catch(error => {
                console.error('删除请求错误:', error);
                showAlert('danger', `删除失败: ${error.message}`);
                this.disabled = false;
                this.innerHTML = '确认删除';
            });
            
            // 隐藏模态框
            modal.hide();
        });
    }
}

function getCSRFToken() {
    // 尝试多种方式获取CSRF token
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
        return metaToken.getAttribute('content');
    }
    
    const inputToken = document.querySelector('input[name="csrf_token"]');
    if (inputToken) {
        return inputToken.value;
    }
    
    // 最后尝试模板变量
    return '{{ csrf_token() }}';
}

function showAlert(type, message) {
    // 移除现有的alert
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());
    
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert" style="margin-bottom: 1rem;">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    `;
    
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertAdjacentHTML('afterbegin', alertHtml);
        
        // 滚动到顶部显示消息
        window.scrollTo({ top: 0, behavior: 'smooth' });
        
        // 自动关闭
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}
</script>

{% endblock %}
